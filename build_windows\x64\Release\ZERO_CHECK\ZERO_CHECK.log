﻿  Checking Build System
  CMake is re-running because D:/Program/Project/project/czcv_camera_new/build_windows/CMakeFiles/generate.stamp is out-of-date.
    the file 'D:/Program/Project/project/czcv_camera_new/CMakeLists.txt'
    is newer than 'D:/Program/Project/project/czcv_camera_new/build_windows/CMakeFiles/generate.stamp.depend'
    result='-1'
  -- Selecting Windows SDK version 10.0.19041.0 to target Windows 10.0.22631.
  for windows
  D:/Program/Project/project/czcv_camera_new/third_party/prebuilt/windows/opencv4.5.1/x64/vc16/staticlib/
  in win32..
  -- opencv version: 4.5.1
  -- Configuring done
  -- Generating done
  -- Build files have been written to: D:/Program/Project/project/czcv_camera_new/build_windows
