import numpy as np

points_dict = {'cam_1080p_100cm_0_no': np.array([[ 186.7099,  109.48993 ],[1077.6725,  126.535225],[1155.025 ,  649.166],
            [92.532455,635.4971],[612.2656,142.12582 ], [608.0081, 357.29434 ],[603.95825 ,  610.45386 ]], dtype=np.float32), 
       'cam_1080p_110cm_0_no': np.array([[ 194.17787 ,  109.76901 ],[1084.7114  ,  127.31352 ],[1163.2987  ,  649.34454 ],
            [ 101.228455,  635.4296  ],[ 619.3509  ,  142.40738 ],[ 615.94934 ,  357.7494  ],[ 612.55396 ,  611.23755 ]], dtype=np.float32), 
       'cam_1080p_120cm_0_no': np.array([[ 200.57547 ,  110.57681 ],[1090.0323  ,  127.658905],
            [1169.3629  ,  649.16907 ],[ 108.64008 ,  635.36646 ],[ 625.3179  ,  142.70224 ],[ 622.03357 ,  358.04355 ],[ 618.6106  ,  612.5383  ]], dtype=np.float32),
       'cam_1080p_130cm_0_no': np.array([[ 205.64444,  110.75156], [1094.2617 ,  128.53206],[1174.4966 ,  649.26306],[ 114.80076,  636.14984],
            [ 630.10144,  142.98654],[ 627.53973,  358.393  ], [ 624.72565,  612.1942 ]], dtype=np.float32), 
       'cam_1080p_140cm_0_no': np.array([[ 210.30557 ,  111.87554 ],[1098.225   ,  129.03539 ],[1179.4243  ,  649.2286  ],[ 119.803215,  636.1601  ],
            [ 634.5198  ,  142.8495  ],[ 631.93066 ,  358.60126 ],[ 629.66895 ,  612.0465  ]], dtype=np.float32),
        'cam_1080p_150cm_0_no': np.array([[ 213.65094 ,  111.69293 ],[1101.3994  ,  128.8947  ],[1182.7148  ,  648.8049  ],
            [ 124.571625,  636.0095  ],[ 638.2324  ,  143.41383 ], [ 636.04913 ,  358.82327 ],[ 633.9974  ,  612.5866  ]], dtype=np.float32), 
       'cam_1080p_160cm_0_no': np.array([[ 217.2509 ,  111.80802],[1104.5405 ,  129.46875], [1187.0612 ,  649.09344],
            [ 128.69107,  636.3788 ],[ 641.25385,  143.63225],[ 639.5738 ,  358.9299 ],[ 637.897  ,  612.7808 ]], dtype=np.float32), 
       'cam_1080p_170cm_0_no': np.array([[ 219.44133,  112.21708],[1106.7655 ,  129.64485],[1190.2402 ,  649.0896 ], [ 131.86458,  636.4432 ],
            [ 644.2124 ,  143.68448],[ 642.6513 ,  359.09128], [ 641.5333 ,  612.67053]], dtype=np.float32)}

point_100 = points_dict['cam_1080p_100cm_0_no']
point_110 = points_dict['cam_1080p_110cm_0_no']
point_120 = points_dict['cam_1080p_120cm_0_no']
point_130 = points_dict['cam_1080p_130cm_0_no']
point_140 = points_dict['cam_1080p_140cm_0_no']
point_150 = points_dict['cam_1080p_150cm_0_no']
point_160 = points_dict['cam_1080p_160cm_0_no']
point_170 = points_dict['cam_1080p_170cm_0_no']

interval = (point_170 - point_100) / 7
print("interval: ", interval)
print(point_110[:, 0], point_100[:, 0] + interval[:, 0])
print(point_120[:, 0], point_100[:, 0] + interval[:, 0]*2)
print(point_130[:, 0], point_100[:, 0] + interval[:, 0]*3)
print(point_140[:, 0], point_100[:, 0] + interval[:, 0]*4)
print(point_150[:, 0], point_100[:, 0] + interval[:, 0]*5)
print(point_160[:, 0], point_100[:, 0] + interval[:, 0]*6)
quit()


print(np.mean(np.abs(point_110[:, 0] - point_100[:, 0] - interval[:, 0])))
print(np.mean(np.abs(point_120[:, 0] - point_100[:, 0] - interval[:, 0]*2)))
print(np.mean(np.abs(point_130[:, 0] - point_100[:, 0] - interval[:, 0]*3)))
print(np.mean(np.abs(point_140[:, 0] - point_100[:, 0] - interval[:, 0]*4)))
print(np.mean(np.abs(point_150[:, 0] - point_100[:, 0] - interval[:, 0]*5)))
print(np.mean(np.abs(point_160[:, 0] - point_100[:, 0] - interval[:, 0]*6)))

