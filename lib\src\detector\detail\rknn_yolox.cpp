// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include "rknn_yolox.h"
#include "rga.h"
//#include "im2d.h"
namespace czcv_camera
{	double __get_us(struct timeval t) { return (t.tv_sec * 1000000 + t.tv_usec); }
    Status YoloxRKNN::init(std::vector<std::string> modelConfig,std::shared_ptr<rga_interface_t> &rgaInterfacePtr,czcv_model_type_t modelType)
    {	
        if(modelConfig.size() <5)
        {
            LOGE("modelConfig size not valid!");
            return CZCV_PARAM_ERR;
        }
		_modelType = modelType;
		int ret;
		if (is_primary())
		{
			ret = rknn_init(&ctx, (void*)modelConfig[4].c_str(), 0, 0, NULL);
			map_size = {{80, 80}, {40, 40}, {20, 20}};
		}
		else
		{
			ret = rknn_init(&ctx, (void*)modelConfig[5].c_str(), 0, 0, NULL);
			map_size = {{40, 40}, {20, 20}, {10, 10}};
		}
		if (ret < 0)
		{
			LOGE("rknn_init error ret=%d\n", ret);
			return -1;
		}

		rknn_core_mask core_mask = RKNN_NPU_CORE_0;
		if (false == is_primary())
		{
			core_mask = RKNN_NPU_CORE_1;
		}

		// ret = rknn_set_core_mask(ctx, core_mask);
		// if (ret < 0)
		// {
		// 	LOGE("rknn_set_core_mask error ret=%d\n", ret);
		// 	return -1;
		// }

		ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
		if (ret < 0)
		{
			LOGE("rknn_init error ret=%d\n", ret);
			return -1;
		}

		input_attrs.resize(io_num.n_input);
		memset(input_attrs.data(), 0, sizeof(rknn_tensor_attr) * io_num.n_input);
		for (int i = 0; i < io_num.n_input; i++)
		{
			input_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
			if (ret < 0)
			{
				LOGE("rknn_init error ret=%d\n", ret);
				return -1;
			}
		}

		output_attrs.resize(io_num.n_output);
		memset(output_attrs.data(), 0, sizeof(rknn_tensor_attr) * io_num.n_output);
		for (int i = 0; i < io_num.n_output; i++)
		{
			output_attrs[i].index = i;
			ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
		}

		if (input_attrs[0].fmt == RKNN_TENSOR_NCHW)
		{
			//LOGE("model is NCHW input fmt, size_with_stride:%d\n", input_attrs[0].size_with_stride);
			_inputChannel = input_attrs[0].dims[1];
			_inputHeight = input_attrs[0].dims[2];
			_inputWidth = input_attrs[0].dims[3];
		}
		else
		{
			//LOGE("model is NHWC input fmt, size_with_stride:%d\n", input_attrs[0].size_with_stride);
			_inputHeight = input_attrs[0].dims[1];
			_inputWidth = input_attrs[0].dims[2];
			_inputChannel = input_attrs[0].dims[3];
		}
		
		_rgaInterfacePtr = rgaInterfacePtr;
		
		this->rgamat_det_pad.handle = _rgaInterfacePtr->malloc_rga(&this->rgamat_det_pad.viraddr, &this->rgamat_det_pad.phyaddr,this->_inputWidth, this->_inputHeight, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);		
		
		input_attrs[0].type = RKNN_TENSOR_UINT8;
		// default fmt is NHWC, npu only support NHWC in zero copy mode
		input_attrs[0].fmt = RKNN_TENSOR_NHWC;
		input_mems[0]      = rknn_create_mem(ctx, input_attrs[0].size_with_stride);

		#define ALIGN 8
		int wstride = _inputWidth + (ALIGN - _inputWidth % ALIGN) % ALIGN;
		int hstride = _inputHeight;

		_rgaInterfacePtr->wrapbuffer_fd(input_mems[0]->fd, _inputWidth, _inputHeight, wstride, hstride, RK_FORMAT_RGB_888);
		
		ret = rknn_set_io_mem(ctx, input_mems[0], &input_attrs[0]);
		if (ret < 0) {
			LOGE("rknn_set_io_mem fail! ret=%d\n", ret);
			return -1;
		}

		for (int i = 0; i < 6; i++)
		{
			int output_size = output_attrs[i].n_elems * sizeof(float);
			// default output type is depend on model, this require float32 to compute top5
			output_attrs[i].type = RKNN_TENSOR_FLOAT32;
			output_mems[i]  = rknn_create_mem(ctx, output_size);

			int num_grid = output_attrs[i].dims[1];

			int outwstride = num_grid + (ALIGN - num_grid % ALIGN) % ALIGN;
			int outhstride = output_attrs[i].dims[2];
			_rgaInterfacePtr->wrapbuffer_fd(output_mems[i]->fd, num_grid, output_attrs[i].dims[2], outwstride, outhstride, RK_FORMAT_RGBA_8888); 			
			ret = rknn_set_io_mem(ctx, output_mems[i], &output_attrs[i]);		
			if (ret < 0) {
				LOGE("rknn_set_io_mem fail! ret=%d\n", ret);
				return -1;
			}   
		}

		GenerateMeshgrid();
		return CZCV_OK;
    }


	
	void YoloxRKNN::generate_grids_and_stride(const int target_size,
										std::vector<int>& strides, std::vector<GridAndStride>& grid_strides)
	{
		int sum_grid = 0;
		for (int i = 0; i < (int)strides.size(); i++)
		{
			int stride = strides[i];
			int num_grid = target_size / stride;
			for (int g1 = 0; g1 < num_grid; g1++)
			{
				for (int g0 = 0; g0 < num_grid; g0++)
				{
					sum_grid += 1;
					GridAndStride gs;
					gs.grid0 = g0;
					gs.grid1 = g1;
					gs.stride = stride;
					grid_strides.push_back(gs);
				}
			}
		}
	}

	void YoloxRKNN::qsort_descent_inplace(std::vector<Object>& objects, int left, int right)
	{
		int i = left;
		int j = right;
		float p = objects[(left + right) / 2].prob;

		while (i <= j)
		{
			while (objects[i].prob > p)
				i++;

			while (objects[j].prob < p)
				j--;

			if (i <= j)
			{
				// swap
				std::swap(objects[i], objects[j]);

				i++;
				j--;
			}
		}

// #pragma omp parallel sections
		{
// #pragma omp section
			{
				if (left < j) qsort_descent_inplace(objects, left, j);
			}
// #pragma omp section
			{
				if (i < right) qsort_descent_inplace(objects, i, right);
			}
		}
	}

	void YoloxRKNN::qsort_descent_inplace(std::vector<Object>& objects)
	{
		if (objects.empty())
			return;

		qsort_descent_inplace(objects, 0, objects.size() - 1);
	}
	
	
	void YoloxRKNN::generate_yolox_proposals(std::vector<GridAndStride> grid_strides,
									float * pred_mat, int num_grid, int num_class, float conf_thre,std::vector<Object>& proposals)
	{
		for (int grid_index = 0; grid_index < num_grid; grid_index++)
		{
			const float *offset_obj_cls_ptr =
				pred_mat + (grid_index * (num_class + 5));
			const int grid0 = grid_strides[grid_index].grid0;
			const int grid1 = grid_strides[grid_index].grid1;
			const int stride = grid_strides[grid_index].stride;
			
			float x_center = (offset_obj_cls_ptr[0] + grid0) * stride;
			float y_center = (offset_obj_cls_ptr[1] + grid1) * stride;
			float w = exp(offset_obj_cls_ptr[2]) * stride;
			float h = exp(offset_obj_cls_ptr[3]) * stride;
			float x0 = x_center - w * 0.5f;
			float y0 = y_center - h * 0.5f;
			
			float box_objectness = offset_obj_cls_ptr[4];

			for (int class_idx = 0; class_idx < num_class; class_idx++)
			{
				float box_cls_score = offset_obj_cls_ptr[5 + class_idx];
				float box_prob = box_objectness * box_cls_score;
				//head thresh is 0.1
				if ((box_prob > conf_thre) || ((1 == class_idx) && (box_prob > 0.1f)))
				{
					Object obj;
					obj.rect.x = x0;
					obj.rect.y = y0;
					obj.rect.width = w;
					obj.rect.height = h;
					obj.label = class_idx;
					obj.prob = box_prob;
					proposals.push_back(obj);
				}
			} // class loop
		}
	}

	
	inline float YoloxRKNN::intersection_area(const Object& a, const Object& b)
	{
		cv::Rect_<float> inter = a.rect & b.rect;
		return inter.area();
	}

	void YoloxRKNN::nms_sorted_bboxes(const std::vector<Object>& objects, 
								std::vector<int>& picked, float nms_threshold)
	{
		picked.clear();

		const int n = objects.size();

		std::vector<float> areas(n);
		for (int i = 0; i < n; i++)
		{
			areas[i] = objects[i].rect.area();
		}

		for (int i = 0; i < n; i++)
		{
			const Object& a = objects[i];

			int keep = 1;
			for (int j = 0; j < (int)picked.size(); j++)
			{
				const Object& b = objects[picked[j]];

				// intersection over union
				float inter_area = intersection_area(a, b);
				float union_area = areas[i] + areas[picked[j]] - inter_area;
				// float IoU = inter_area / union_area
				if (inter_area / union_area > nms_threshold)
					keep = 0;
			}

			if (keep)
				picked.push_back(i);
		}
	}


	//**********yolov10后处理**********//
	void YoloxRKNN::GenerateMeshgrid() {
		meshgrid.clear();
		for (int index = 0; index < head_num; ++index) {
			for (int i = 0; i < map_size[index][0]; ++i) {
				for (int j = 0; j < map_size[index][1]; ++j) {
					meshgrid.push_back(j + 0.5f);
					meshgrid.push_back(i + 0.5f);
				}
			}
		}
	}

	// Sigmoid函数
	inline float sigmoid(float x) {
		return 1.0f / (1.0f + std::exp(-x));
	}

	// TopK筛选
	std::vector<DetectBox> YoloxRKNN::TopK(const std::vector<DetectBox>& detectResult) {
		if (detectResult.size() <= topK) {
			return detectResult;
		}
		
		// 按置信度降序排序
		std::vector<DetectBox> sorted = detectResult;
		std::sort(sorted.begin(), sorted.end(), 
			[](const DetectBox& a, const DetectBox& b) {
				return a.score > b.score;
			});
		
		// 取前topK个
		return std::vector<DetectBox>(sorted.begin(), sorted.begin() + topK);
	}

	// 计算两个边界框的IoU（交并比）
	float YoloxRKNN::calculateIoU(const DetectBox& box1, const DetectBox& box2) {
		// 计算交集区域的坐标
		float x1 = std::max(box1.xmin, box2.xmin);
		float y1 = std::max(box1.ymin, box2.ymin);
		float x2 = std::min(box1.xmax, box2.xmax);
		float y2 = std::min(box1.ymax, box2.ymax);
		
		// 如果没有交集，返回0
		if (x2 <= x1 || y2 <= y1) {
			return 0.0f;
		}
		
		// 计算交集面积
		float intersection = (x2 - x1) * (y2 - y1);
		
		// 计算两个框的面积
		float area1 = (box1.xmax - box1.xmin) * (box1.ymax - box1.ymin);
		float area2 = (box2.xmax - box2.xmin) * (box2.ymax - box2.ymin);
		
		// 计算并集面积
		float unionArea = area1 + area2 - intersection;
		
		// 避免除零
		if (unionArea <= 0) {
			return 0.0f;
		}
		
		return intersection / unionArea;
	}

	// NMS非极大值抑制算法
	std::vector<DetectBox> YoloxRKNN::applyNMS(const std::vector<DetectBox>& boxes, float nms_threshold) {
		if (boxes.empty()) {
			return {};
		}
		
		// 按置信度降序排序
		std::vector<DetectBox> sorted_boxes = boxes;
		std::sort(sorted_boxes.begin(), sorted_boxes.end(), 
			[](const DetectBox& a, const DetectBox& b) {
				return a.score > b.score;
			});
		
		std::vector<bool> suppressed(sorted_boxes.size(), false);
		std::vector<DetectBox> result;
		
		for (size_t i = 0; i < sorted_boxes.size(); ++i) {
			if (suppressed[i]) {
				continue;
			}
			
			// 保留当前框
			result.push_back(sorted_boxes[i]);
			
			// 抑制与当前框IoU大于阈值的其他框
			for (size_t j = i + 1; j < sorted_boxes.size(); ++j) {
				if (suppressed[j]) {
					continue;
				}
				
				// 只对同一类别进行NMS
				if (sorted_boxes[i].classId == sorted_boxes[j].classId) {
					float iou = calculateIoU(sorted_boxes[i], sorted_boxes[j]);
					
					// 只有在有IoU交集的情况下才进行直觉重合度判断
					if (iou > 0.1f) {
						// 计算水平方向的重叠
						float x1 = std::max(sorted_boxes[i].xmin, sorted_boxes[j].xmin);
						float x2 = std::min(sorted_boxes[i].xmax, sorted_boxes[j].xmax);
						float width_intersection = (x2 > x1) ? (x2 - x1) : 0.0f;
						
						// 计算并集宽度（两个框合并后的总宽度）
						float union_x_min = std::min(sorted_boxes[i].xmin, sorted_boxes[j].xmin);
						float union_x_max = std::max(sorted_boxes[i].xmax, sorted_boxes[j].xmax);
						float union_width = union_x_max - union_x_min;
						
						// 如果直觉重合度很高（超过80%），即使IoU不高也要抑制
						bool should_suppress = false;
						if (union_width > 0.0f) {
							// 直觉重合度 = 重叠宽度 / 并集宽度
							float intuitive_overlap_ratio = width_intersection / union_width;
							if (intuitive_overlap_ratio > 0.6f) {
								should_suppress = true;
							} else if (iou > nms_threshold) {
								should_suppress = true;
							}
						} else {
							if (iou > nms_threshold) {
								should_suppress = true;
							}
						}
						
						if (should_suppress) {
							suppressed[j] = true;
						}
					} else if (iou > nms_threshold) {
						// 如果没有IoU交集，只使用传统IoU判断
						suppressed[j] = true;
					}
				}
			}
    	}		
		return result;
	}

	// 后处理主函数
	std::vector<DetectBox> YoloxRKNN::postprocess(rknn_tensor_mem** outputs, 
									int img_h, int img_w,float ratio,float dw,float dh) {
		
		std::vector<DetectBox> detectResult;
		int gridIndex = -2;
		
		// 计算缩放比例
		float scale_h = static_cast<float>(img_h) / _inputHeight;
		float scale_w = static_cast<float>(img_w) / _inputWidth;
		
		for (int index = 0; index < head_num; ++index) {
			float* reg = (float*)outputs[index * 2]->virt_addr;
			float* cls = (float*)outputs[index * 2 + 1]->virt_addr;
			
			const int grid_h = map_size[index][0];
			const int grid_w = map_size[index][1];
			const int grid_size = grid_h * grid_w;
			
			for (int h = 0; h < grid_h; ++h) {
				for (int w = 0; w < grid_w; ++w) {
					gridIndex += 2;
					
					// 获取最大类别分数
					float cls_max = 0.0f;
					int cls_index = 0;
					
					if (class_num == 1) {
						cls_max = sigmoid(cls[h * grid_w + w]);
						cls_index = 0;
					} else {
						for (int cl = 0; cl < class_num; ++cl) {
							float cls_val = cls[cl * grid_size + h * grid_w + w];
							if (cl == 0) {
								cls_max = cls_val;
								cls_index = cl;
							} else {
								if (cls_val > cls_max) {
									cls_max = cls_val;
									cls_index = cl;
								}
							}
						}
						// cls_max = sigmoid(cls_max);
					}
					
					// 应用置信度阈值
					float class_threshold = (cls_index == 1) ? 0.05f : conf_thres();
					if (cls_max > class_threshold) {
						// 处理回归值
						std::vector<float> regdfl(4, 0.0f);
						
						for (int lc = 0; lc < 4; ++lc) {
							float sfsum = 0.0f;
							float locval = 0.0f;
							
							float maxv = -FLT_MAX;
							for (int df = 0; df < 16; ++df) {
								int idx = ((lc * 16) + df) * grid_size + h * grid_w + w;
								if (reg[idx] > maxv)
								{
									maxv = reg[idx];
								}
							}

							// 计算softmax
							for (int df = 0; df < 16; ++df) {
								int idx = ((lc * 16) + df) * grid_size + h * grid_w + w;
								float temp = std::exp(reg[idx] - maxv);
								reg[idx] = temp;
								sfsum += temp;
							}
	
							// 计算位置值
							for (int df = 0; df < 16; ++df) {
								int idx = ((lc * 16) + df) * grid_size + h * grid_w + w;
								float sfval = reg[idx] / sfsum;
								locval += sfval * df;
							}
							
							regdfl[lc] = locval;
						}
						
						// 计算边界框坐标
						float x1 = (meshgrid[gridIndex] - regdfl[0]) * strides[index];
						float y1 = (meshgrid[gridIndex + 1] - regdfl[1]) * strides[index];
						float x2 = (meshgrid[gridIndex] + regdfl[2]) * strides[index];
						float y2 = (meshgrid[gridIndex + 1] + regdfl[3]) * strides[index];
						
						// float xmin = x1 - dw;
						float xmin = x1;
						xmin /= ratio;
						// float ymin = y1 - dh;
						float ymin = y1;
						ymin /= ratio;
						// float xmax = x2 - dw;
						float xmax = x2;
						xmax /= ratio;
						// float ymax = y2 - dh;
						float ymax = y2;
						ymax /= ratio;
						

						// 边界检查
						xmin = std::max(0.0f, xmin);
						ymin = std::max(0.0f, ymin);
						xmax = std::min(static_cast<float>(img_w), xmax);
						ymax = std::min(static_cast<float>(img_h), ymax);
						
						// 添加到结果集
						detectResult.emplace_back(cls_index, cls_max, xmin, ymin, xmax, ymax);
					}
				}
			}
		}
		// 先进行TopK筛选
		std::vector<DetectBox> topk_result = TopK(detectResult);
		
		// 然后应用NMS
		std::vector<DetectBox> nms_result = applyNMS(topk_result, nms_thres());
		
		return nms_result;
	}
	//**********yolov10后处理**********//

    Status YoloxRKNN::run(DetInputOutput &inputOutput)
    {
        cv::Mat bgr;
        inputOutput.ref_frame_to(bgr);
		int frame_h = inputOutput.get_frame_h();
		int frame_w = inputOutput.get_frame_w();
		int frame_channels = inputOutput.get_frame_channels();

		int img_height = frame_h;
		if (frame_channels == 1)
		{
			img_height = img_height * 2 / 3;
		}
		int img_width = frame_w;
		int w = img_width;
		int h = img_height;
		float scale = 1.f;
		if (w > h)
		{
			scale = (float)_inputWidth / w;
			w = _inputWidth;
			h = h * scale;			
		}
		else
		{
			scale = (float)_inputHeight / h;
			h = _inputHeight;
			w = w * scale;
		}
	
		cv::Mat mat_rs;
		int wpad = _inputWidth - w;
		int hpad = _inputHeight - h;
		int ret;
		//cv::Mat bgr_resize;
		if (frame_channels == 4)
		{
			cv::resize(bgr, mat_rs, cv::Size(w, h), 0, 0, cv::INTER_LINEAR);
			cv::cvtColor(mat_rs, mat_rs, cv::COLOR_RGBA2BGR);
		}
		else if (frame_channels == 1)
		{
			w -= (w & 1);
			h -= (h & 1);
			wpad = _inputWidth - w;
			hpad = _inputHeight - h;
			
			cv::Mat dstnv21 = cv::Mat::zeros(h * 3 / 2, w, CV_8UC1);
			cv::Rect rect;
			cv::Rect dstRect;
			nv21resize(bgr, dstnv21, rect, dstRect);
			mat_rs = dstnv21;
			int code =  cv::COLOR_YUV2BGR_NV21;
			if (NV12_format == inputOutput.format())
			{
				code = cv::COLOR_YUV2BGR_NV12;
			}
			cv::cvtColor(mat_rs, mat_rs, code);
		}
		else
		{
			ret = _rgaInterfacePtr->mat_fill(input_mems[0]->fd,_inputWidth,_inputHeight,0,0,0);
			ret = _rgaInterfacePtr->cropScaleRgb(inputOutput.get_input_phyadrr(),input_mems[0]->fd,bgr.cols,bgr.rows,0,0,bgr.cols,bgr.rows,_inputWidth,_inputHeight,0,0,w,h);
		}

		rknn_input inputs[1];
		memset(inputs, 0, sizeof(inputs));
		inputs[0].index = 0;
		inputs[0].type = RKNN_TENSOR_UINT8;
		inputs[0].size = _inputWidth * _inputHeight * _inputChannel;
		inputs[0].fmt = RKNN_TENSOR_NHWC;
		inputs[0].pass_through = 0;
		inputs[0].buf = rgamat_det_pad.viraddr;

		rknn_output outputs[io_num.n_output];
		memset(outputs, 0, sizeof(outputs));
		for (int i = 0; i < io_num.n_output; i++)
		{
			outputs[i].index = i;
			outputs[i].want_float = 1;
		}

		// 执行推理
		ret = rknn_run(ctx, NULL);		
		std::vector<DetectBox> predbox = postprocess(output_mems, img_height, img_width,scale,wpad,hpad);
		
		// 处理person和head的匹配与筛选逻辑
		std::vector<DetectBox> filtered_predbox;

		// 分离person和head检测框
		std::vector<DetectBox> person_boxes, head_boxes;
		for (const auto& box : predbox) {
			if (box.classId == 0) {  // person类别
				person_boxes.push_back(box);
			} else if (box.classId == 1) {  // head类别
				head_boxes.push_back(box);
			}		
		}
		
		std::set<int> head_ids;
		// 处理每个person框
		for (const auto& person : person_boxes) {
			std::vector<DetectBox> matched_heads;
			std::vector<int> matched_head_ids;
			// 找到所有属于这个person的head框
			for (size_t i = 0; i < head_boxes.size(); i++)
			//for (const auto& head : head_boxes) 
			{
				const auto& head = head_boxes[i];
				// 计算head框和person框的交集面积
				float x1 = std::max(head.xmin, person.xmin);
				float y1 = std::max(head.ymin, person.ymin);
				float x2 = std::min(head.xmax, person.xmax);
				float y2 = std::min(head.ymax, person.ymax);
				
				// 如果有交集
				if (x2 > x1 && y2 > y1) {
					float intersection_area = (x2 - x1) * (y2 - y1);
					float head_area = (head.xmax - head.xmin) * (head.ymax - head.ymin);
					
					// 计算head框有多少比例在person框内
					float overlap_ratio = intersection_area / head_area;
					
					// 如果超过70%，认为head属于这个person
					if (overlap_ratio > 0.7f) {
						matched_heads.push_back(head);
						matched_head_ids.push_back(i);
					}
				}
			}
			
			// 如果有匹配的head框
			if (!matched_heads.empty()) {
				// 保留置信度最高的head框
				int best_match_id = 0;
				DetectBox best_head = matched_heads[0];
				for (size_t j = 1; j < matched_heads.size(); j++)
				{
					const auto& head = matched_heads[j];
					if (head.score > best_head.score) {
						best_head = head;
						best_match_id = j;
					}
				}
				
				// 添加到结果中：person框 + 最佳head框
				filtered_predbox.push_back(person);
				if (head_ids.count(matched_head_ids[best_match_id]) == 0)
				{
					filtered_predbox.push_back(best_head);
					head_ids.insert(matched_head_ids[best_match_id]);
				}	
			} else {
				// 如果没有匹配的head框，检查person置信度
				if (person.score >= 0.5f) {
					// 置信度>=0.5，保留person框
					filtered_predbox.push_back(person);
				}
			}
		}
		
		// 使用过滤后的结果替换原来的predbox
		predbox = filtered_predbox;
		for (size_t i = 0; i < head_boxes.size(); i++)
		{
			auto & box = head_boxes[i];
			if (box.score >= 0.5f && head_ids.count(i) == 0)
			{
				predbox.push_back(box);
			}	
		}

		for (auto box : predbox)
		{
			BboxF bboxF(box.xmin, box.ymin, box.xmax, box.ymax, box.score, box.classId);
			inputOutput.push_one_bbox(bboxF); 
		}

		ret = rknn_outputs_release(ctx, io_num.n_output, outputs);

        return CZCV_OK;
    }

}//namespace czcv_mobile

