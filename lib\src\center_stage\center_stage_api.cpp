#include "center_stage/center_stage_api.h"
#include "center_stage/czcv_center_stage.h"

namespace czcv_camera
{
    Android_API::Android_API()
    {
        _currentMode = enTrackModeVideo;//default enTrackModeVideo
    }
    enTrackMode Android_API::get_mode()
    {
        std::unique_lock<std::mutex> ulk(_muMode);
        return _currentMode;
    }
    Android_API::~Android_API()
    {
        if(stager != nullptr)
        {
            reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->release();
            czcv_camera::PersonCenterStager* stager_ = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager);

            delete stager_;
            stager_ = nullptr;
        }
    }

    int Android_API::init_api(std::string modeldir, int src_width, int src_height, int dst_width, int dst_height, bool bnv12, int only_cpu, int use_4k, int gesture_mode, 
            czcv_doa_callback pfun_doa_callback, czcv_gesture_event_callback pfun_gesture_event_callback, czcv_camera_led_callback pfun_camera_led_callback, czcv_camera_event_callback pfun_camera_event_callback)
    {     
        _modeldir = modeldir;
        _src_width = src_width;
        _src_height = src_height;
        _dst_width = dst_width;
        _dst_height = dst_height;
        _bnv12 = bnv12;

        stager = (void*)(new czcv_camera::PersonCenterStager(src_width, src_height));
        // base viewer 是简单的拿第一个window做crop,resize回原大小
        std::shared_ptr<czcv_camera::Base_PersonViewer>  personViewer =
                std::make_shared<czcv_camera::Base_PersonViewer>();

        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->set_use4k(use_4k);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->bind_viewer(personViewer);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->set_detector(czcv_camera::DetectorID::Yolox_PERSON);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->set_tracker(czcv_camera::TrackerID::BYTE);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->out_width(dst_width);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->out_height(dst_height);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->gesture_mode(gesture_mode);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->assert_path(_modeldir);

        std::vector<std::string> modelConfig;
        modelConfig.push_back(modeldir+"/czcv_0001.bin");
        modelConfig.push_back(modeldir+"/czcv_0002.bin");
        modelConfig.push_back(modeldir+"/czcv_0001_gpu.bin");
        modelConfig.push_back(modeldir+"/czcv_0002_gpu.bin");
        modelConfig.push_back(modeldir + "/czcv_0001.rknn");  
        modelConfig.push_back(modeldir + "/czcv_0002.rknn"); 
        modelConfig.push_back(modeldir + "/czcv_0006.rknn"); 
        //modelConfig.push_back(modeldir + "/yolox_ghostnet-640-640_rk3576.rknn");

        Status s = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->init_models_async(modelConfig,only_cpu);
        if (s!= CZCV_OK)
        {
            LOGE("init_models_async failed!\n");
            return -1;
        }
        
        std::vector<std::string> modelConfig_assert;
        modelConfig_assert.push_back(modeldir + "/czcv_0003.bin");
        modelConfig_assert.push_back(modeldir + "/czcv_0004.bin");
        modelConfig_assert.push_back(modeldir + "/czcv_0003.rknn");   
        s = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->init_model_assert(modelConfig_assert,only_cpu);
        if (s!= CZCV_OK)
        {
            LOGE("init_model_assert failed!\n");
            return -1;
        }

        std::vector<std::string> modelConfig_hand;
        modelConfig_hand.push_back(modeldir + "/czcv_0005.rknn"); 
        s = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->init_model_hand(modelConfig_hand,only_cpu);
        if (s != CZCV_OK)
        {
            LOGE("init_model_hand failed!\n");
            return -1;
        }

        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->set_nv12(bnv12);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->doa_callback(pfun_doa_callback);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->gesture_event_callback(pfun_gesture_event_callback);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->camera_led_callback(pfun_camera_led_callback);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->camera_event_callback(pfun_camera_event_callback);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->start();

        cv::setNumThreads(1);

        return 0;
    }

    int Android_API::run_api(cv::Mat& frame, int phy_addr, void* dst_vir_addr, int dst_phy_addr,int Cmode,int low_consumption)
    {  
        //LOGE(" +++++ Android_API::run_api------ %d",Cmode);
        czcv_camera::ImageBlob imageBlob;
        imageBlob.frameCopiedBGR = frame;
        imageBlob.phy_addr = phy_addr;

        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->dst_vir_addr(dst_vir_addr);
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->dst_phy_addr(dst_phy_addr);
        Status ret = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->run(imageBlob,Cmode,low_consumption);
        //frame = imageBlob.frameCopiedBGR.clone();
        return ret;
    }

    int Android_API::run_api_sub(cv::Mat& frame, int phy_addr)
    {  
        czcv_camera::ImageBlob imageBlob;
        imageBlob.frameCopiedBGR = frame;
        imageBlob.phy_addr = phy_addr;

        Status ret = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->run_sub(imageBlob);

        return ret;
    }

    int Android_API::set_mode(enTrackMode mode)
    {
        std::unique_lock<std::mutex> ulk(_muMode);

        czcv_camera::PersonCenterStager* stager_ = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager);
        // if(mode == enTrackModeNo)
        // {
        //     stager_->stop_async();
        // }
        // else
        // {
        //     if (_currentMode == enTrackModeNo)
        //     {
        //         stager_->start();
        //     }
        // }

        stager_->mode(mode);
        _currentMode = mode;
        
        return 0;
    }

    int Android_API::set_gesture_mode(int mode)
    {
        reinterpret_cast<czcv_camera::PersonCenterStager*>(stager)->gesture_mode(mode);
        return 0;
    }

    void Android_API::set_person_boxes(void* boxinfo, int boxnum,void* vir_addr, int width, int height)
    {
        PersonBBox* data_ptr = reinterpret_cast<PersonBBox*>(boxinfo);
        std::vector<PersonBBox> personBoxes;
        personBoxes.resize(boxnum);
        for (int i = 0;i < boxnum; i++)
        {
            personBoxes[i] = data_ptr[i];
        }
        czcv_camera::PersonCenterStager* stager_ = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager);
        stager_->person_boxes(personBoxes,vir_addr,width,height);
    }

    void Android_API::get_view_window(int * x0, int * y0, int * x1, int* y1)
    {
        czcv_camera::PersonCenterStager* stager_ = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager);
        BboxF window(*x0, *y0, *x1, *y1);
        stager_->view_window(window);
        *x0 = window.xmin();
        *y0 = window.ymin();
        *x1 = window.xmax();
        *y1 = window.ymax();
    }

    void* Android_API::get_rga_handle()
    {
        czcv_camera::PersonCenterStager* stager_ = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager);
        return stager_->get_rga_handle();
    }

    int Android_API::inject_event(int event)
    {
        czcv_camera::PersonCenterStager* stager_ = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager);
        return stager_->inject_event(event);
    }


    
#ifdef _WIN32
    void Android_API::set_debug_info(TrackerInputOutput& t, TrackerInputOutput& t_sub, float doa, float doa_sub)
    {
        czcv_camera::PersonCenterStager* stager_ = reinterpret_cast<czcv_camera::PersonCenterStager*>(stager);
        stager_->set_debug_info(t, t_sub, doa, doa_sub);
    }
#endif
} // namespace czcv_camera
