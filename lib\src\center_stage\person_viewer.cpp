// Copyright (C) 2021 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include "center_stage/person_viewer.h"


namespace czcv_camera
{

    Status Base_PersonViewer::bind_callback(std::shared_ptr<Abstarct_PersonViewer_DataCallback> &dataCallbackPtr)
    {
        _dataCallbackPtr = dataCallbackPtr;
        return  CZCV_OK;
    }
    Status Base_PersonViewer::bind_dewarper(std::shared_ptr<BaseCamDewarper> &camDewarperPtr)
    {
        _camDewarperPtr = camDewarperPtr;
        return CZCV_OK;
    }
    Status Base_PersonViewer::bind_ops_interface(std::shared_ptr<rga_interface_t> &rgaInterfacePtr)
    {
        _rgaInterfacePtr = rgaInterfacePtr;
        return CZCV_OK;
    }

    bool PUBLIC g_debug_roi = false;
    int PUBLIC g_debug_phy_addr = -1;
    BboxF PUBLIC g_debug_bbox;
    
    Status Base_PersonViewer::on_process_frame_roi(cv::Mat &frameIn, cv::Mat &frameOut,
                                               std::vector<BboxF> &windows, int phy_addr, 
                                               void* dst_vir_addr, int dst_phy_addr, int & roi_height)
    {
        g_debug_roi = true;
        g_debug_phy_addr = phy_addr;
        g_debug_bbox = windows[0];

        int inwidth = preview_width();
        int inheight = preview_height();

        int outwidth = ((out_width() + 15) & (~15));
        int outheight = ((out_height() + 7) & (~7));
        if (outwidth == 0 || outheight == 0)
        {
            outwidth = frameOut.cols;
            outheight = frameOut.rows;
            if (frameOut.channels() == 1)
            {
                //outheight = frameOut.rows * 2 / 3;
                outheight = frameOut.rows * 5 / 7;
            }
        }
        
        // view as you designed
        // initial demo is only do center crop
        if(windows.empty())
        {
            LOGE("windows should not be empty!\n");
            return CZCV_OK;
        }

        cv::Rect srcrect = windows[0].cv_rect();
        //cv::Rect dstrect(0, 0, outwidth, outheight);
        //LOGE("on_process_frame_roi: %d %d %d %d %f %f %f %f\n", srcrect.x, srcrect.y, srcrect.width, srcrect.height, windows[0].xmin(), windows[0].ymin(), windows[0].xmax(), windows[0].ymax());

        if (_rgaInterfacePtr != nullptr && _rgaInterfacePtr->cropScaleRoi != nullptr)
        {               
            srcrect.x = srcrect.x - (srcrect.x & 1);
            srcrect.y = srcrect.y - (srcrect.y & 1);
            srcrect.width = srcrect.width - (srcrect.width & 1);
            srcrect.height = srcrect.height - (srcrect.height & 1);
            
            int ret;
            if (dst_phy_addr == -1)
            {
                LOGE("invalid dst_phy_addr!\n");
                return CZCV_OK;
            }
           
            int dst_roi_height = outheight * 5 / 7;
            dst_roi_height = dst_roi_height - (dst_roi_height & 1);
            roi_height = dst_roi_height;
     
            ret = _rgaInterfacePtr->cropScaleRoi(phy_addr, dst_phy_addr, ((inwidth + 7) & (~7)), ((inheight + 7) & (~7)), srcrect.x, srcrect.y, srcrect.width, srcrect.height, outwidth, outheight, 0, 0, outwidth, dst_roi_height);    
            if (ret != 0)
            {
                LOGE("_rgaInterfacePtr scale failed: %d\n", ret);
            }
        }

        return CZCV_OK;    
    }

    Status Base_PersonViewer::on_process_frame(cv::Mat &frameIn, cv::Mat &frameOut,
                                               std::vector<BboxF> &windows, int phy_addr, 
                                               void* dst_vir_addr, int dst_phy_addr)
    {
        g_debug_roi = false;
        g_debug_phy_addr = phy_addr;
        g_debug_bbox = windows[0];

        int inwidth = preview_width();
        int inheight = preview_height();

        int outwidth = ((out_width() + 15) & (~15));
        int outheight = ((out_height() + 7) & (~7));
        if (outwidth == 0 || outheight == 0)
        {
            outwidth = frameOut.cols;
            outheight = frameOut.rows;
            if (frameOut.channels() == 1)
            {
                outheight = frameOut.rows * 2 / 3;
            }
        }    
        
        // view as you designed
        // initial demo is only do center crop
        if(windows.empty())
        {
            LOGE("windows should not be empty!\n");
            return CZCV_OK;
        }

        cv::Rect srcrect = windows[0].cv_rect();
        //cv::Rect dstrect(0, 0, outwidth, outheight);
        //LOGE("on_process_frame: %d %d %d %d %f %f %f %f\n", srcrect.x, srcrect.y, srcrect.width, srcrect.height, windows[0].xmin(), windows[0].ymin(), windows[0].xmax(), windows[0].ymax());

        if (_rgaInterfacePtr != nullptr && _rgaInterfacePtr->cropScale != nullptr)
        {               
            srcrect.x = srcrect.x - (srcrect.x & 1);
            srcrect.y = srcrect.y - (srcrect.y & 1);
            srcrect.width = srcrect.width - (srcrect.width & 1);
            srcrect.height = srcrect.height - (srcrect.height & 1);
            
            int ret;
            if (dst_phy_addr == -1)
            {
                LOGE("invalid dst_phy_addr!\n");
                return CZCV_OK;
            }
           
            ret = _rgaInterfacePtr->cropScale(phy_addr, dst_phy_addr, ((inwidth + 15) & (~15)), ((inheight + 7) & (~7)), srcrect.x, srcrect.y, srcrect.width, srcrect.height, outwidth, outheight);                   
            if (ret != 0)
            {
                LOGE("_rgaInterfacePtr scale failed: %d\n", ret);
            }
        }
        else
        {
            /*if (dst_vir_addr != nullptr)
            {
                frameOut = cv::Mat(outheight * 3 / 2, outwidth, CV_8UC1, dst_vir_addr);
            }
            else
            {
                frameOut = cv::Mat::zeros(outheight * 3 / 2, outwidth, CV_8UC1);
            }
            if (_mapx.empty())
            {
                _mapx = cv::Mat::zeros(outheight, outwidth, CV_32FC1);
                _mapy = cv::Mat::zeros(outheight, outwidth, CV_32FC1);
                float invw = 1.0f / (outwidth - 1);
                float invh = 1.0f / (outheight - 1);
                for (int j = 0; j < outwidth; j++)
                {
                    _mapx.col(j) = j * invw;
                }
                for (int i = 0; i < outheight; i++)
                {
                    _mapy.row(i) = i * invh;
                }
            }
            cv::Rect2f srcrect = windows[0].cv_rect2f();
            cv::Mat mapx = srcrect.x + srcrect.width * _mapx;
            cv::Mat mapy = srcrect.y + srcrect.height * _mapy;
            
            nv21remap(frameIn, frameOut, mapx, mapy);*/

            //nv21resize(frameIn, frameOut, srcrect, dstrect);
            //frameOut.copyTo(frameIn);
        }

        return CZCV_OK;    
    }


    Status Base_PersonViewer::process_merge(std::vector<BboxF>& rects, std::vector<std::vector<int>>& merged_indeces)
    {
        merged_indeces.resize(rects.size());
        for(size_t i = 0;i < merged_indeces.size(); i++)
        {
            merged_indeces[i].push_back(i);
        }

        std::vector<BboxF> merge_rects = rects;
        while (1)
        {
            bool bmerge = false;
            for(size_t i = 0; i < merge_rects.size(); i++)
            {
                if (0 == merged_indeces[i].size())
                {
                    continue;
                }

                for(size_t j = i + 1; j  < merge_rects.size(); j++)
                {
                    if (0 == merged_indeces[j].size())
                    {
                        continue;
                    }

                    if (merge_rects[i].intersect_with(merge_rects[j]) > 0.4f * (std::min)(merge_rects[j].area(), merge_rects[i].area()) )
                    {
                        merged_indeces[i].insert(merged_indeces[i].end(), merged_indeces[j].begin(), merged_indeces[j].end());                     
                        merged_indeces[j].clear();

                        merge_rects[i].xmin((std::min)(merge_rects[i].xmin(), merge_rects[j].xmin()));
                        merge_rects[i].xmax((std::max)(merge_rects[i].xmax(), merge_rects[j].xmax()));
                        merge_rects[i].ymin((std::min)(merge_rects[i].ymin(), merge_rects[j].ymin()));
                        merge_rects[i].ymax((std::max)(merge_rects[i].ymax(), merge_rects[j].ymax()));

                        bmerge = true;
                    }                 
                }
            }

            if (false == bmerge)
            {
                break;
            }
        }    

        return CZCV_OK;
    }

}

