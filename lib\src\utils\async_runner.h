// async detect then track
// Author: lih<PERSON><PERSON><EMAIL>
// MIT License


#ifndef CV_ASYNC_DEMO_RUNNER_H
#define CV_ASYNC_DEMO_RUNNER_H

#include "base/status.h"
#include "detector/base_detector.h"
#include "tracker/base_tracker.h"
#include <thread>

namespace czcv_camera
{
    typedef  enum _RunnerEvent
    {
        NONE,
        NORMAL_RUNNING,
        STOP_LOOP
    } AsyncRunnerEvent;


    /**
     * @brief 检测子任务
     * 比如检测关键帧，或者拍摄的图像
     * 主任务是帧速率自适应的，会丢弃一些帧
     * 这个子任务用于补充上述的缺点
     */
    class Runner_Sub_Detect_Job
    {
    public:
        Runner_Sub_Detect_Job()
        {
            _callbackDone = false;

        }
        cv::Mat imgBGR; ///< in
        DetInputOutput detInfo;///< out
        virtual void callback(DetInputOutput &det)
        {
            // callback 中不能做耗时任务, 是同步回调
            detInfo = det;
            set_callback_done(true);
        }
        Status wait_and_get_dets(DetInputOutput &dets)
        {
            //TODO 等待超时逻辑
            LOGI("wait_and_get_dets...\n");
            while (!callback_done())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }
            dets = detInfo;
            LOGI("get_dets finish!\n");
            return CZCV_OK;
        }

    private:
        bool  callback_done()
        {
            std::unique_lock<std::mutex> ulk(_mu);
            return  _callbackDone;
        }
        void set_callback_done(bool  v)
        {
            std::unique_lock<std::mutex> ulk(_mu);
            _callbackDone = true;
        }
        bool _callbackDone;
        std::mutex _mu;
    };

    class Async_Preview_Runner
    {
    public:
        Async_Preview_Runner();
        ~Async_Preview_Runner();
        void set_max_fps(int fps){ _maxFPS = fps; }

        void  bind_detector(std::shared_ptr<BaseObjectDetector> & detector);
        void  bind_tracker(std::shared_ptr<BaseTracker> & tracker);

        Status init_models(std::vector<std::string> &modelPaths, bool  isAsync=false);

        Status start();

        Status push_frame(cv::Mat &frameBGR);

        Status fetch_dets(DetInputOutput &dets);

        Status stop();

        Status submit_once_sub_detect_job(Runner_Sub_Detect_Job *job);

        void set_gparams(DynamicParams &gParams)
        {
            _gParams = gParams;
        }

        void set_nv12(bool bnv12)
        {
            _bnv12 = bnv12;
        }

    private:
        bool frame_ready()
        {
            std::unique_lock<std::mutex> ulk(_muFrame);
            return  _frameReady;
        }
        void set_frame_ready(bool v)
        {
            std::unique_lock<std::mutex> ulk(_muFrame);
            _frameReady = v;
        }

        void set_event(AsyncRunnerEvent e)
        {
            std::unique_lock<std::mutex> ulk(_mutEvent);
            _event = e;
        }
        AsyncRunnerEvent get_event()
        {
            std::unique_lock<std::mutex> ulk(_mutEvent);
            return  _event;
        }
        DetInputOutput get_dets()
        {
            std::unique_lock<std::mutex> ulk(_muDet);
            return  _dets;
        }
        void set_dets(DetInputOutput &d)
        {
            std::unique_lock<std::mutex> ulk(_muDet);
            _dets = d;
        }
        void get_frame(cv::Mat &ref, bool withCopy = false)
        {
            std::unique_lock<std::mutex> ulk(_muFrame);
            if(withCopy)
            {
                ref = _frame.clone();
            }
            else
            {
                ref = _frame;
            }
        }
        void  set_frame(cv::Mat &frame)
        {
            std::unique_lock<std::mutex> ulk(_muFrame);
            _frame = frame;
            _frameReady = true;
        }
        bool do_sub_job()
        {
            std::unique_lock<std::mutex> ulk(_muSubJob);
            return _doSubJob;
        }
        void  set_do_sub_job(bool  v)
        {
            std::unique_lock<std::mutex> ulk(_muSubJob);
            _doSubJob = v;
        }
        bool is_running()
        {
            std::unique_lock<std::mutex> ulk(_muIsRun);
            return _isRunning;
        }
        void set_is_running(bool v)
        {
            std::unique_lock<std::mutex> ulk(_muIsRun);
            _isRunning = v;
        }

        void init_models_thread_fn();
        void do_sub_detect_job_fn();
        void thread_loop();


        int _maxFPS;
        std::mutex _muDet, _muFrame, _mutEvent, _muSubJob, _muIsRun;
        AsyncRunnerEvent _event;
        cv::Mat _frame;
        bool _frameReady;
        bool _doSubJob;
        bool _isRunning;
        bool _modelInited;
        DetInputOutput _dets;
        Runner_Sub_Detect_Job * _subJob;
        std::shared_ptr<BaseObjectDetector>  _detector;
        std::shared_ptr<BaseTracker>  _tracker;
        std::vector<std::string> _modelPaths;

        czcv_camera::DynamicParams _gParams;
        bool _bnv12 = false;
    };

    class AsyncFrameWorker
    {
    public:
        AsyncFrameWorker()
        {
            _frameReady = false;
            _frameReadySub = false;
            _isRunning = false;
            _event = NORMAL_RUNNING;
        }
        virtual  ~AsyncFrameWorker(){}
        virtual  Status start_frame_worker()
        {
            std::thread t;
            t = std::thread(&AsyncFrameWorker::thread_loop, this);
            t.detach();
            return  CZCV_OK;
        }

        virtual  Status push_frame(cv::Mat &frame)
        {
            if(!frame.data)
            {
                LOGE("[Async_Preview_Runner] frame data in null!\n");
                return  CZCV_PARAM_ERR;
            }
            //如果帧是ready的， 说明还没有被消费完，直接跳过
            // if(frame_ready() )
            // {
            //     return  CZCV_OK;
            // }
            //设置完后， frame_ready() 会变成true
            set_frame(frame);
            return CZCV_OK;
        }
        virtual  Status stop()
        {
            set_event(STOP_LOOP);
            while (is_running())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(5));
            }
            return  CZCV_OK;
        }

        virtual  void thread_loop()
        {
            while(true)
            {
                AsyncRunnerEvent event = get_event();

                switch (event)
                {
                    case NORMAL_RUNNING:
                    {
                        cv::Mat frame;
                        //如果帧还没有准备好，那么轮询等待
                        if(!frame_ready())
                        {
                            std::this_thread::sleep_for(std::chrono::milliseconds(10));
                            continue;
                        }
                        std::shared_ptr<rga_interface_t> rgaInterfacePtr = nullptr;
						int a = 0;
                        get_frame(a,0, rgaInterfacePtr, false);

                        frame_worker(frame,0);

                        set_frame_ready(false);
                    }
                    break;

                    case STOP_LOOP:
                    {
                        LOGI("AsyncFrameWorker::thread_loop stopped!\n");
                        _event = NORMAL_RUNNING;
                        set_is_running(false);
                        return;
                    }
                    default:
                        break;
                }
            }
        }

        virtual  void frame_worker(cv::Mat &frame,int Cmode) = 0;

    protected:
        bool frame_ready(bool primaty=true)
        {
            if (primaty)
            {
                std::unique_lock<std::mutex> ulk(_muFrame);
                return  _frameReady;
            }
            else
            {
                std::unique_lock<std::mutex> ulk(_muFrameSub);
                return  _frameReadySub;
            }
        }
        void set_frame_ready(bool v, bool primaty=true)
        {
            if (primaty)
            {
                std::unique_lock<std::mutex> ulk(_muFrame);
                _frameReady = v;
            }
            else
            {
                std::unique_lock<std::mutex> ulk(_muFrameSub);
                _frameReadySub = v;
            }       
        }

        void set_event(AsyncRunnerEvent e)
        {
            std::unique_lock<std::mutex> ulk(_mutEvent);
            _event = e;
        }
        AsyncRunnerEvent get_event()
        {
            std::unique_lock<std::mutex> ulk(_mutEvent);
            return  _event;
        }

        void get_frame(int &ret_phyaddr, int _frame_phyaddr, std::shared_ptr<rga_interface_t> &rgaInterfacePtr,bool withCopy = false, int down_scale=1)
        {
            if(withCopy)
            {
                int ret = rgaInterfacePtr->cropScale(_frame_phyaddr, ret_phyaddr, _frame.cols, _frame.rows * 2 / 3, 0, 0, _frame.cols, _frame.rows * 2 / 3, _frame.cols / down_scale, _frame.rows * 2 / 3 / down_scale);
                if(ret != 0)
                {
                    LOGE("get_frame rga cropScale failed!\n");
                }         
            }
        }

        void get_frame(cv::Mat &ref, bool withCopy = false)
        {
            std::unique_lock<std::mutex> ulk(_muFrame);
            if(withCopy)
            {
                ref = _frame.clone();
            }
            else
            {
                ref = _frame;
            }
        }

        virtual void  set_frame(cv::Mat &frame)
        {
            std::unique_lock<std::mutex> ulk(_muFrame);
            _frame = frame.clone();
            _frameReady = true;
        }

        bool is_running(bool primary=true)
        {
            if (primary)
            {
                std::unique_lock<std::mutex> ulk(_muIsRun);
                return _isRunning;
            }
            else
            {
                std::unique_lock<std::mutex> ulk(_muIsRunSub);
                return _isRunningSub;
            }         
        }

        void set_is_running(bool v, bool primary=true)
        {
            if (primary)
            {
                std::unique_lock<std::mutex> ulk(_muIsRun);
                _isRunning = v;
            }
            else
            {
                std::unique_lock<std::mutex> ulk(_muIsRunSub);
                _isRunningSub = v;
            }
        }

        bool ges_is_running()
        {
            std::unique_lock<std::mutex> ulk(_muIsRunGes);
            return _isRunningGes;       
        }

        void ges_set_is_running(bool v)
        {
            std::unique_lock<std::mutex> ulk(_muIsRunGes);
            _isRunningGes = v;
        }

        std::mutex _muFrame,_muFrameSub, _mutEvent, _muIsRun, _muIsRunSub, _muIsRunGes;
        AsyncRunnerEvent _event;
        cv::Mat _frame;
        bool _frameReady = false;
        bool _frameReadySub = false;
        bool _isRunning = false;
        bool _isRunningSub = false;
        bool _isRunningGes = false;
    };

}


#endif //CV_ASYNC_DEMO_RUNNER_H
