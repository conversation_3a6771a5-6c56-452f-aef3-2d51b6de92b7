// Copyright (C) 2021 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include "tracker/base_tracker.h"
#include "tracker/tracker_factory.h"
#include <opencv2/opencv.hpp>
#include "detail/kalmanFilter.h"
#include "../utils/lapjv.h"
using namespace cv;
using namespace std;
// #include "BYTETracker.h"

namespace czcv_camera
{

 	enum TrackState { New = 0, Tracked, Lost, Removed };

	class STrack
	{
	public:
		STrack(vector<float> tlwh_, float score, int label=-1, int detid=-1);
		~STrack();

		vector<float> static tlbr_to_tlwh(vector<float> &tlbr);
		void static multi_predict(vector<STrack*> &stracks, czcv_camera::KalmanFilter &kalman_filter);
		void static_tlwh();
		void static_tlbr();
		vector<float> tlwh_to_xyah(vector<float> tlwh_tmp);
		vector<float> to_xyah();
		void mark_lost();
		void mark_removed();
		int next_id();
		int end_frame();
		
		void activate(czcv_camera::KalmanFilter &kalman_filter, int frame_id);
		void re_activate(STrack &new_track, int frame_id, bool new_id = false);
		void update(STrack &new_track, int frame_id);

	public:
		bool is_activated;
		int track_id;
		int state;

		vector<float> _tlwh;
		vector<float> tlwh;
		vector<float> tlbr;
		int frame_id;
		int tracklet_len;
		int start_frame;

		czcv_camera::KAL_MEAN mean;
		czcv_camera::KAL_COVA covariance;
		float score;
		int label;
		int _detid;

	private:
		czcv_camera::KalmanFilter kalman_filter;
	};


    struct Object
	{
		cv::Rect_<float> rect;
		int label;
		float prob;
	};

    class CZCV_BYTETracker : public BaseTracker {
    public:
        virtual~CZCV_BYTETracker()
        {

        }
        virtual Status init(std::vector<std::string> modelConfig) override;
        virtual Status on_set_arg() override;
        virtual Status sub_run(TrackerInputOutput &inputOutput,int use_4k) override;
        // Scalar get_color(int idx);
    private:
		vector<STrack*> joint_stracks(vector<STrack*> &tlista, vector<STrack> &tlistb);
		vector<STrack> joint_stracks(vector<STrack> &tlista, vector<STrack> &tlistb);

		vector<STrack> sub_stracks(vector<STrack> &tlista, vector<STrack> &tlistb);
		void remove_duplicate_stracks(vector<STrack> &resa, vector<STrack> &resb, vector<STrack> &stracksa, vector<STrack> &stracksb);

		void linear_assignment(vector<vector<float> > &cost_matrix, int cost_matrix_size, int cost_matrix_size_size, float thresh,
			vector<vector<int> > &matches, vector<int> &unmatched_a, vector<int> &unmatched_b);
		vector<vector<float> > iou_distance(vector<STrack*> &atracks, vector<STrack> &btracks, int &dist_size, int &dist_size_size);
		vector<vector<float> > iou_distance(vector<STrack> &atracks, vector<STrack> &btracks);
		vector<vector<float> > ious(vector<vector<float> > &atlbrs, vector<vector<float> > &btlbrs);

		double lapjv(const vector<vector<float> > &cost, vector<int> &rowsol, vector<int> &colsol, 
			bool extend_cost = false, float cost_limit = LONG_MAX, bool return_cost = true);

    private:
        float track_thresh = 0.25;
		float det_thresh = 0.3;
		float match_thresh = 0.99;
		float iou_thresh = 0.03;
		float low_thresh = 0.2;
		int frame_id;
		int max_time_lost = 32;
		int max_time_lost_hidden = 10;

		vector<STrack> tracked_stracks;
		vector<STrack> lost_stracks;
		vector<STrack> removed_stracks;
		czcv_camera::KalmanFilter kalman_filter;
    };
}//namespace czcv_mobile