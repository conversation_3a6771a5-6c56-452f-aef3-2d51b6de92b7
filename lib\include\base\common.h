// Copyright (C) 2021 CZUR Limited. All rights reserved.
// Only CZUR inner use.


#ifndef CZCV_CAMERA_COMMON_H
#define CZCV_CAMERA_COMMON_H

#include "base/macro.h"
#include <opencv2/opencv.hpp>

namespace  czcv_camera
{
    #define CZCV_GESTURE_PALM          (0) //20
    #define CZCV_GESTURE_STOP          (1)  //24
    #define CZCV_GESTURE_PEACE         (2)  //21
    #define CZCV_GESTURE_PEACE_INV     (3)  //22
    #define CZCV_GESTURE_THUMB_INDEX   (9999)//(6)  //31

    typedef enum
    {
        CZCV_LOG_INFO,
        CZCV_LOG_WARN,
        CZCV_LOG_ERR
    } LOG_LEVEL;

    enum ImageFormat
    {
        YUYV_format,
        RGB_format,
        YUV422_format,
        NV21_format, ///< for android
        NV12_format,
        BGR_format
    };

    typedef struct _RgaMat{
		void* viraddr;
		int phyaddr;
		void* handle;
        _RgaMat()
        {
            viraddr = nullptr;
            phyaddr = -1;
            handle = nullptr;
        }
	}RgaMat;


    class PUBLIC ImageBlob
	{
	public:
		ImageBlob();
		ImageBlob(const ImageBlob &b);
		ImageBlob & operator=(const ImageBlob &b);

		cv::Mat frameCopiedBGR; ///< for no buffer manager, e.g imread,Note: imgformat invalid when set it
		char * buffer;
		int height;///< frame height
		int width; ///< frame width
		int stride; ///< data stride
		int index;///< V4L2 buffer index
		ImageFormat imgformat; ///< see @ImageFormat
		timeval     timestamp; ///< mostly from camera driver
		int         frameCounter; ///<global frame  counter value
        int phy_addr;
	};

    void nv21resize(cv::Mat& nv21, cv::Mat& dstnv21, cv::Rect& rect, cv::Rect& dstrect);
    void nv21remap(cv::Mat& nv21, cv::Mat& dstnv21, cv::Mat& mapx, cv::Mat& mapy);

    typedef int (*rgaScaleFun)(int, int, int, int, int);
    typedef int (*rgaCropScaleFun)(int, int, int, int, int, int, int, int, int, int);
    typedef int (*rgaInitFun)(int, int, void**);
    typedef int (*rgaCopyFun)(int);
    typedef int (*rgaMergeFun)(int srcfd, int ratio);

    /*0.2.0*/
    // typedef int (*rgaCropScaleRgbFun)(int, int, int, int, int, int, int, int, int, int);
    typedef int (*rgaCropScaleRgbFun)(int , int , int , int , int , int , int , int , int , int , int , int , int , int );
    typedef int (*rgaNv12torgbFun)(int , int , int , int , int , int );
    typedef int (*rgaMatfillFun)(int , int , int , int , int , int );
    typedef void* (*rgaMallocFun)(void** , int* , int, int, int);
    typedef int (*rgaFreeFun)(void*);
    typedef void (*rgaWrapbufferFdFun)(int fd, int width, int height, int wstride, int hstride, int format);

    typedef int (*rgaCropScaleRoiFun)(int srcfd, int dstfd, int src_width, int src_height, int rectx, int recty, int rectw, int recth, int dst_width, int dst_height, int dstrectx, int dstrecty, int dstrectw, int dstrecth);

    typedef struct rga_interface {
        rgaInitFun init;
        rgaScaleFun scale;
        rgaCopyFun copy;
        rgaCropScaleFun cropScale;
        rgaMergeFun merge;

        /*v0.2.0*/
        rgaCropScaleRgbFun cropScaleRgb;
        rgaNv12torgbFun yuv2rgb;
        rgaMatfillFun mat_fill;
        rgaMallocFun malloc_rga;
        rgaFreeFun free;
        rgaWrapbufferFdFun wrapbuffer_fd;

        rgaCropScaleRoiFun cropScaleRoi;
    } rga_interface_t;

    typedef enum {
        HAL_PIXEL_FORMAT_RGBA_8888 = 1,
        HAL_PIXEL_FORMAT_RGBX_8888 = 2,
        HAL_PIXEL_FORMAT_RGB_888 = 3,
        HAL_PIXEL_FORMAT_RGB_565 = 4,
        HAL_PIXEL_FORMAT_BGRA_8888 = 5,
        HAL_PIXEL_FORMAT_YCBCR_422_SP = 16,
        HAL_PIXEL_FORMAT_YCRCB_420_SP = 17,
        HAL_PIXEL_FORMAT_sRGB_A_8888        = 0xC,  // 12
        HAL_PIXEL_FORMAT_sRGB_X_8888        = 0xD,  // 13

        HAL_PIXEL_FORMAT_YCrCb_NV12         = 0x15, // YUY2, 21
        HAL_PIXEL_FORMAT_YCrCb_NV12_VIDEO   = 0x16, // 22, 和 HAL_PIXEL_FORMAT_RGBA_FP16 的 value 重叠,
                                                    // 但目前 HAL_PIXEL_FORMAT_YCrCb_NV12_VIDEO 已经不被实际使用.
        HAL_PIXEL_FORMAT_YCrCb_NV12_10      = 0x17, // YUY2_1obit, 23
        HAL_PIXEL_FORMAT_YCbCr_422_SP_10    = 0x18, // 24
        HAL_PIXEL_FORMAT_YCrCb_420_SP_10    = 0x19, // 25

        HAL_PIXEL_FORMAT_YUV420_8BIT_I      = 0x1A, // 420I 8bit, 26
        HAL_PIXEL_FORMAT_YUV420_10BIT_I     = 0x1B, // 420I 10bit, 27
        HAL_PIXEL_FORMAT_Y210               = 0x1C, // 422I 10bit, 28
        HAL_PIXEL_FORMAT_BGR_888            = 29,

        HAL_PIXEL_FORMAT_BPP_1              = 0x30, // 48
        HAL_PIXEL_FORMAT_BPP_2              = 0x31, // 49
        HAL_PIXEL_FORMAT_BPP_4              = 0x32, // 50
        HAL_PIXEL_FORMAT_BPP_8              = 0x33, // 51
        HAL_PIXEL_FORMAT_YCBCR_422_I = 20,
        HAL_PIXEL_FORMAT_RGBA_FP16 = 22,
        HAL_PIXEL_FORMAT_RAW16 = 32,
        HAL_PIXEL_FORMAT_BLOB = 33,
        HAL_PIXEL_FORMAT_IMPLEMENTATION_DEFINED = 34,
        HAL_PIXEL_FORMAT_YCBCR_420_888 = 35,
        HAL_PIXEL_FORMAT_RAW_OPAQUE = 36,
        HAL_PIXEL_FORMAT_RAW10 = 37,
        HAL_PIXEL_FORMAT_RAW12 = 38,
        HAL_PIXEL_FORMAT_RGBA_1010102 = 43,
        HAL_PIXEL_FORMAT_Y8 = 538982489,
        HAL_PIXEL_FORMAT_Y16 = 540422489,
        HAL_PIXEL_FORMAT_YV12 = 842094169,
    } czcv_pixel_format_t;

    typedef enum {
        EN_MODEL_TYPE_CPU = 0,
        EN_MODEL_TYPE_GPU = 1,
        EN_MODEL_TYPE_NPU = 2,
    } czcv_model_type_t;
}
#endif //CZCV_CAMERA_COMMON_H
