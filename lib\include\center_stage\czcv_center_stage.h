// Copyright (C) 2021 CZUR Limited. All rights reserved.
// Only CZUR inner use.
// Author: l<PERSON><PERSON><PERSON><PERSON><PERSON>@czur.com

#ifndef CZCV_CAMERA_CZCV_CENTER_STAGE_H
#define CZCV_CAMERA_CZCV_CENTER_STAGE_H

#include <base/common.h>
#include <base/dynamic_param.h>
#include <base/status.h>
#include "center_stage/center_stage_capi.h"
#include "detector/detector_factory.h"
#include "tracker/tracker_factory.h"
#include "person_viewer.h"

namespace  czcv_camera
{
    typedef struct _PersonBBox
    {
        int person_x0, person_y0, person_x1, person_y1; // 人物整体的坐标，两点坐标法确定一个框体
        float person_conf; // 置信度
    }PersonBBox;

    class  PersonCenterStagerImpl;
    class  PUBLIC PersonCenterStager
    {
    public:
        PersonCenterStager(int preview_width, int preview_height, LOG_LEVEL logLevel = CZCV_LOG_INFO);
        ~PersonCenterStager();

        Status set_detector(DetectorID detectorId);
        Status set_tracker(TrackerID trackerId);
        Status on_set_arg(DynamicParams& params);
        Status bind_viewer(std::shared_ptr<Base_PersonViewer> &personViewerPtr);

        /**
         * @brief init algorithm models
         * @param modelDir
         * @return @Status
         */
        Status init_models_async(std::vector<std::string>& detModelCfg, int only_cpu);
        Status init_model_assert(std::vector<std::string>& assertModelCfg, int only_cpu);
        Status init_model_hand(std::vector<std::string>& assertModelCfg, int only_cpu);
        /************************************************************/

        /**
         *
         * @return  @Status
         */
        Status start();

        /**
         * @brief  push frame to
         * @param img_blob  see @ImageBlob
         * @return  @Status
         */
        Status push_frame(ImageBlob& imgBlob);
        /************************************************************/

        Status run(ImageBlob &imgBlob,int Cmode,int low_consumption);
        void set_nv12(bool bnv12);
        void set_use4k(int use_4k);
        void release();
        void dst_vir_addr(void* dst_vir_addr);
        void dst_phy_addr(int dst_phy_addr);
        void mode(enTrackMode mode);
        void out_width(int out_width);
        void out_height(int out_height);
        void stop_async();
        void person_boxes(const std::vector<PersonBBox> & person_boxes,void* vir_addr, int width, int height);
        void view_window(BboxF& window);
        void doa_callback(czcv_doa_callback callback);
        void* get_rga_handle();
        void gesture_event_callback(czcv_gesture_event_callback callback);
        void camera_led_callback(czcv_camera_led_callback callback);
        Status run_sub(ImageBlob &imgBlob);
        void gesture_mode(bool mode);
        void assert_path(std::string assertpath);
        int inject_event(int event);
        void camera_event_callback(czcv_camera_event_callback callback);
        void set_debug_info(TrackerInputOutput& t, TrackerInputOutput& t_sub, float doa, float doa_sub);
    private:
        std::shared_ptr<PersonCenterStagerImpl> _impl;
    };

    typedef struct 
    {
        int instace_id;
        int gesture_id;
        int count;
        int miss_count;
        bool bnew;
        BboxF box;
    }stGestureInfo;
}//namespace  czcv_camera

#endif //CZCV_CAMERA_CZCV_CENTER_STAGE_H
