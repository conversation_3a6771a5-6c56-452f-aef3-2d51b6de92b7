#ifndef CZCV_CAMERA_ANDROID_API_H
#define CZCV_CAMERA_ANDROID_API_H

#include<iostream>
#include<opencv2/opencv.hpp>
#include "base/macro.h"
#include "center_stage/center_stage_capi.h"
#include "tracker/base_tracker.h"
#include <mutex>
// #include "czcv_mobile/center_stage/czcv_center_stage.h"


namespace czcv_camera
{
    class PUBLIC Android_API
    {
    public:
        Android_API();
        int init_api(std::string modeldir, int src_width, int src_height, int dst_width, int dst_height, bool bnv12=false, int only_cpu=0, int use_4k=1, int gesture_mode=1, 
                czcv_doa_callback pfun_doa_callback=nullptr, czcv_gesture_event_callback pfun_gesture_event_callback=nullptr, czcv_camera_led_callback pfun_camera_led_callback=nullptr,
                czcv_camera_event_callback pfun_camera_event_callback=nullptr);
        int run_api(cv::Mat& frame, int phy_addr=-1, void* dst_vir_addr=nullptr, int dst_phy_addr=-1,int Cmode=0,int low_consumption = 0);
        int set_mode(enTrackMode mode);
        ~Android_API();
        // czcv_mobile::PersonCenterStager stager;
        enTrackMode get_mode();
        void set_person_boxes(void* boxinfo, int boxnum,void* vir_addr, int width, int height);
        void get_view_window(int* x0, int* y0, int* x1, int* y1);
        void* get_rga_handle();
        int run_api_sub(cv::Mat& frame, int phy_addr);
        int set_gesture_mode(int mode);

        int inject_event(int event);
#ifdef _WIN32
        void set_debug_info(TrackerInputOutput& t, TrackerInputOutput& t_sub, float doa, float doa_sub);
#endif
    private:
        enTrackMode _currentMode;
        void* stager = nullptr;
        std::string _modeldir;
        int _src_width;
        int _src_height;
        int _dst_width;
        int _dst_height;
        bool _bnv12;
        std::mutex _muMode;
    };
}


#endif